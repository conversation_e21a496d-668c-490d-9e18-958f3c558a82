"use client";

import Button from "@/components/Button";
import Container from "@/components/Container";
import Typography from "@/components/Typography";
import CallUsModal from "@/components/CallUsModal";
import useStore from "@/hooks/useStore";
import { observer } from "mobx-react-lite";
import { Content } from "@prismicio/client";
import { PrismicNextImage, PrismicNextLink } from "@prismicio/next";
import { SliceComponentProps } from "@prismicio/react";
import { PrismicRichText } from "@prismicio/react";
import Link from "next/link";
import MemoPhoneIcon from "@/assets/icons/PhoneIcon";
import MemoWhatsAppIcon from "@/assets/icons/WhatsAppIcon";
import * as styles from "./DiscountBannerSection.css";

export type DiscountBannerSectionProps =
  SliceComponentProps<Content.DiscountBannerSectionSlice>;
const DiscountBannerSection = observer(({
  slice,
}: DiscountBannerSectionProps): JSX.Element => {
  const { landign } = useStore();
  return (
    <Container notFullHeight withAnimation>
      <section
        className={styles.root}
        data-slice-type={slice.slice_type}
        data-slice-variation={slice.variation}
      >
        <div className={styles.container}>
          <div className={styles.titleRow}>
            <Typography
              variant="subTitleMedium"
              className={styles.areaTitle}
            >
              {slice.primary.area_title}
            </Typography>
            <div className={styles.titleButtons}>
              <button
                onClick={() => landign.setCallUsModalIsOpen(true)}
                className={styles.titleContactButton}
                title="Call Us"
              >
                <MemoPhoneIcon />
              </button>
              <Link
                href="https://wa.me/+442034881249"
                target="_blank"
                className={styles.titleContactButton}
                title="WhatsApp"
              >
                <MemoWhatsAppIcon />
              </Link>
            </div>
          </div>

          <div className={styles.mainContent}>
            <div className={styles.imageWrapper}>
              <PrismicNextImage
                field={slice.primary.image}
                className={styles.image}
              />
            </div>

            <div className={styles.contentBlock}>
              <Typography
                className={styles.highlightTitle}
              >
                {slice.primary.highlight_title}
              </Typography>

              <Typography
                variant="bodyMedium"
                className={styles.descriptionDesktop}
              >
                <PrismicRichText field={slice.primary.description} />
              </Typography>
              <Typography
                variant="bodySmall"
                className={styles.descriptionMobile}
              >
                <PrismicRichText field={slice.primary.description} />
              </Typography>

              <Button
                as={PrismicNextLink}
                field={slice.primary.button_link}
                color="primary"
                size="normal"
                className={styles.button}
              >
                <Typography variant="buttonMedium" className={styles.buttonText}>
                  {slice.primary.button_label}
                </Typography>
              </Button>


            </div>
          </div>
        </div>
        <CallUsModal
          phoneNumber="0800 046 1000"
          open={landign.callUsModalIsOpen}
          onClose={() => landign.setCallUsModalIsOpen(false)}
        />
      </section>
    </Container>
  );
});

export default DiscountBannerSection;
